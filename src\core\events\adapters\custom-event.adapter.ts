import { globalEventManager } from '../event-manager.ts';

export function setupDispatchEventBridge(eventNames: string[]) {
    const handleEvent = (event: Event) => {
        const customEvent = event as CustomEvent;

        globalEventManager.publish(`${customEvent.type}`, customEvent.detail);
    };

    eventNames.forEach(eventName => {
        window.addEventListener(eventName, handleEvent);
    });

    return () => {
        eventNames.forEach(eventName => {
            window.removeEventListener(eventName, handleEvent);
        });
    };
}