import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import { defineConfig } from 'eslint/config';

export default defineConfig([
  {
      files: ['**/*.{js,mjs,cjs,ts,mts,cts}'],
      plugins: { js },
      extends: ['js/recommended'],
      languageOptions: { globals: globals.browser },
      rules: {
          quotes: ['error', 'single'],
          'semi': ['error', 'always'],
          'no-explicity-any': ["warn", "always"]
      }
  },
  tseslint.configs.recommended,
]);
