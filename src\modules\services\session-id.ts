import { StoreManager } from "../../core/store/store-manager.ts";
import { preScript } from "../../core/providers/prescript.ts";
import { api } from "../../core/api/client.ts";
import { Logger } from "../../core/helpers/logger.ts";

export function getSessionId(): string {
    const storeManager = new StoreManager();

    try {
        const baseUrl = preScript.get
    } catch (error) {
        Logger.error(`Failed to get session id: ${error}`);
    }
}