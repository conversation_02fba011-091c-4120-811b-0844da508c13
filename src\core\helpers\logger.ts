const css = {
    base: 'padding: 3px 6px; border-radius: 4px; font-weight: bold;',
    info:    'background: #2196F3; color: #ffffff;',
    debug:   'background: #9C27B0; color: #ffffff;',
    warn:    'background: #FF9800; color: #000000;',
    error:   'background: #f44336; color: #ffffff;',
    success: 'background: #4CAF50; color: #ffffff;',
    text:    'color: inherit; padding: 2px 0;',
    code:    'color: #00e676; font-family: monospace;',
};

export class Logger {
    static info(msg: string) {
        console.log(`%c[INFO]%c ${msg}`, `${css.base} ${css.info}`, css.text);
    }

    static debug(msg: string, code?: any) {
        console.log(`%c[DEBUG]%c ${msg}`, `${css.base} ${css.debug}`, css.text);
        if (code !== undefined) {
            console.debug(`%c>> %c${Logger.stringify(code)}`, 'color: #9e9e9e;', css.code);
        }
    }

    static warn(msg: string) {
        console.warn(`%c[WARN]%c ${msg}`, `${css.base} ${css.warn}`, css.text);
    }

    static error(msg: string, error?: any) {
        console.error(`%c[ERROR]%c ${msg}`, `${css.base} ${css.error}`, css.text);
        if (error) {
            console.error(`%c>> %c${Logger.stringify(error)}`, 'color: #ef9a9a;', css.code);
        }
    }

    static success(msg: string) {
        console.log(`%c[SUCCESS]%c ${msg}`, `${css.base} ${css.success}`, css.text);
    }

    private static stringify(data: any): string {
        try {
            return typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        } catch (e) {
            return String(data);
        }
    }
}
