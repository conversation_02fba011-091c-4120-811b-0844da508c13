import { globalEventManager } from "../event-manager.ts";
import { Logger } from "../../helpers/logger.ts";

interface PostMessageFormat {
    type: string;
    payload: any;
}

export function setupPostMessageBridge(trustedOrigins: string[]): () => void {
    const handleMessage = (event: MessageEvent<PostMessageFormat>) => {
        if (!trustedOrigins.includes(event.origin)) {
            return;
        }

        const { type, payload } = event.data;

        const eventName = `postmessage:${type}`;
        Logger.info(`Mensagem recebida de origem confiável. Publicando evento: ${eventName}`);
        globalEventManager.publish(eventName, payload);
    };

    window.addEventListener('message', handleMessage);

    return () => {
        window.removeEventListener('message', handleMessage);
        Logger.info("Listener de PostMessage removido.");
    };
}