export class Styles {
    static getStyle(name: string) {
        return getComputedStyle(document.documentElement).getPropertyValue(name);
    }

    static setStyle(name: string, value: string) {
        document.documentElement.style.setProperty(name, value);
    }

    static removeStyle(name: string) {
        document.documentElement.style.removeProperty(name);
    }

    static applyStyleSheet(url: string) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;
        document.head.appendChild(link);
    }
}