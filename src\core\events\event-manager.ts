import {setupDispatchEventBridge} from "./adapters/custom-event.adapter.ts";
import {Logger} from "../helpers/logger.ts";
import {setupPostMessageBridge} from "./adapters/post-message.adapter.ts";

type Callback<T = any> = (payload: T) => void;
type Subscribers = Map<string, Set<Callback>>;


export class EventManager {
    private subscribers: Subscribers = new Map();

    public subscribe<T>(eventName: string, callback: Callback<T>): () => void {
        if (!this.subscribers.has(eventName)) {
            this.subscribers.set(eventName, new Set());
        }

        const eventSubscribers = this.subscribers.get(eventName)!;
        eventSubscribers.add(callback);

        return () => this.unsubscribe(eventName, callback);
    }

    public unsubscribe<T>(eventName: string, callback: Callback<T>): void {
        const eventSubscribers = this.subscribers.get(eventName);
        if (eventSubscribers) {
            eventSubscribers.delete(callback);
            if (eventSubscribers.size === 0) {
                this.subscribers.delete(eventName);
            }
        }
    }

    public publish<T>(eventName: string, payload: T): void {
        const eventSubscribers = this.subscribers.get(eventName);
        if (eventSubscribers) {
            eventSubscribers.forEach(callback => {
                try {
                    callback(payload);
                } catch (error) {
                    Logger.error(`Error when trying to execute callback "${eventName}":`, error);
                }
            });
        }
    }

    public subscribeCustomEvents(eventNames: string[]) {
        return setupDispatchEventBridge(eventNames);
    }

    public subscribePostMessages(trustedOrigins: string[]) {
        return setupPostMessageBridge(trustedOrigins);
    }

    public dispatch(eventName: string, payload: any) {
        const event = new CustomEvent(eventName, { detail: payload });
        window.dispatchEvent(event);
    }

    public postMessage(iframeEl: HTMLIFrameElement, eventName: string, payload: any, targetOrigin: string): void {
        if (!iframeEl || !iframeEl.contentWindow) {
            Logger.error("EventManager: target iframe is not available.");
            return;
        }

        const message = {
            type: eventName,
            payload: payload
        };

        iframeEl.contentWindow.postMessage(message, targetOrigin);
    }
}

export const globalEventManager = new EventManager();

