{"name": "new-implantation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@eslint/js": "^9.34.0", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@typescript-eslint/utils": "^8.42.0", "eslint": "^9.34.0", "globals": "^16.3.0", "jiti": "^2.5.1", "typescript": "~5.8.3", "typescript-eslint": "^8.42.0", "vite": "^7.1.2"}, "dependencies": {"axios": "^1.11.0"}}