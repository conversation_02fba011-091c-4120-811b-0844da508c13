import { Logger } from "../helpers/logger";

const WEEK_IN_MILISECONDS = 604800000;

export interface CookieParams {
    name: string;
    value: string;
    path?: string;
    expires?: Date;
}

export class StoreManager {

    public setItem<T>(key: string, value: T): void {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            Logger.error(`Failed to set item in store: ${error}`);
        }
    }

    public getItem<T>(key: string): T | null {
        try {
            const itemToGet = localStorage.getItem(key);
            return itemToGet ? JSON.parse(itemToGet) : null;
        } catch (error) {
            Logger.error(`Failed to get item from store: ${error}`);
            return null;
        }
    }

    public clearStore(): void {
        localStorage.clear();
    }

    public removeItem(key: string): void {
        localStorage.removeItem(key);
    }

    public getCookie(name: string): string | null {
        const cookieMatched = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
        if (cookieMatched) {
            return decodeURIComponent(cookieMatched[2]);
        }

        return null;
    }

    public setCookie(cookie: CookieParams): void {
        const {
            name,
            value,
            path = "/",
            expires = new Date(Date.now() + WEEK_IN_MILISECONDS)
        } = cookie;

        let cookieProperties = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
        cookieProperties += `; expires=${expires.toUTCString()}`;
        cookieProperties += `; path=${path}`;
        cookieProperties += `; domain=${window.location.hostname.replace('www', '.')}`;

        document.cookie = cookieProperties;
    }

    public removeCookie(name: string, path = "/"): void {
        document.cookie = `${encodeURIComponent(name)}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}`;
    }

    public clearCookies(): void {
        const cookies = document.cookie.split("; ");
        cookies.forEach(cookie => {
            const [key] = cookie.split("=");
            this.removeCookie(key);
        });
    }

}

export const storeManager = new StoreManager();