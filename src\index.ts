import {EventManager} from "./core/events/event-manager.ts";
import { modulesProvider } from "./core/providers/modules.ts";
import {preScript} from "./core/providers/prescript.ts";
import {Logger} from "./core/helpers/logger.ts";

window.SizebayPrescript = () => ({
    getModules(): string[] {
        return ['similar', 'tryon', "complementar"];
    }
})

async function bootstrap() {
    const eventManager = new EventManager();


    const modulesToLoad = preScript.modules;

    await modulesProvider.load(modulesToLoad);

    eventManager.publish('app:ready', { loadedModules: modulesToLoad });
}

bootstrap().then(() => Logger.info('App inicializada com sucesso.'));
