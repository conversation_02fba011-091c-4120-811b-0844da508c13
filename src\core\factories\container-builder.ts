type ContainerType = "div" | "section" | "iframe";

type ContainerElementMap = {
    div: HTMLDivElement;
    section: HTMLElement;
    iframe: HTMLIFrameElement;
};

type Container<T extends ContainerType = "div"> = {
    id: string;
    name: string;
    className?: string;
    type?: T;
}

export class ContainerBuilder {
    public static execute<T extends ContainerType = "div">({ id, type = "div" as T, name, className }: Container<T>): ContainerElementMap[T] {
        const container = document.createElement(type);
        container.id = id;
        container.setAttribute("name", name);
        container.setAttribute("class", className || "");

        return container as ContainerElementMap[T];
    }
}