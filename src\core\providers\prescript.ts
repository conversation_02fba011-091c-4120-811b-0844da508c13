interface SizebayPrescriptObject {
    [key: string]: () => any;
}

declare global {
    interface Window {
        SizebayPrescript?: () => SizebayPrescriptObject;
    }
}

class PreScript {
    get tenantId() {
        return this.safelyGetFromPrescript('getTenantId') || '';
    }

    get collectionName() {
        return this.safelyGetFromPrescript('getCollectionName') || '';
    }

    get permalink() {
        return this.safelyGetFromPrescript('getPermalink') || '';
    }

    get lang() {
        return this.safelyGetFromPrescript('getLang') || '';
    }

    get perPage() {
        return this.safelyGetFromPrescript('getPerPage') || 0;
    }

    get baseVrfUrl() {
        return this.safelyGetFromPrescript('getBaseVrfUrl') || '';
    }

    get baseFashionHintUrl() {
        return this.safelyGetFromPrescript('getBaseFashionHintUrl') || '';
    }

    get sizeSystem() {
        return this.safelyGetFromPrescript('getSizeSystem') || '';
    }

    get environment() {
        return this.safelyGetFromPrescript('getEnvironment') || '';
    }

    get currency() {
        return this.safelyGetFromPrescript('getCurrency') || '';
    }

    get defaultUnit() {
        return this.safelyGetFromPrescript('getDefaultUnit') || '';
    }

    get similarityThreshold() {
        return this.safelyGetFromPrescript('getSimilarityThreshold') || 0;
    }

    get similarContainerId() {
        return this.safelyGetFromPrescript('getSimilarContainerId') || '';
    }

    get complementaryContainerId() {
        return this.safelyGetFromPrescript('getComplementaryContainerId') || '';
    }

    get modules() {
        return this.safelyGetFromPrescript('getModules') || [];
    }

    public safelyGetFromPrescript = (key: string) => {
        const ps = window?.SizebayPrescript;
        if (!ps || typeof ps !== 'function') {
            return false;
        }
        const prescriptObj = ps();
        if (typeof prescriptObj?.[key] === 'function') {
            return prescriptObj[key]();
        }
        return false;
    }
}

export const preScript = new PreScript();