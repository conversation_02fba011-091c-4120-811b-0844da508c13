import type { <PERSON><PERSON><PERSON>Loader } from "../interfaces/module.ts";
import {Logger} from "../helpers/logger.ts";

class ModulesProvider {
    private modules: Record<string, ModuleLoader> = {};

    public registry(): Record<string, ModuleLoader> {
        this.modules.similar = () => import('../../modules/similar');
        this.modules.complementar = () => import('../../modules/complementar');
        this.modules.tryon = () => import('../../modules/tryon');

        return this.modules;
    }

    async load(moduleNames: string[]): Promise<void> {
        const availableModules = modulesProvider.registry();

        const loadPromises = moduleNames.map(async (name) => {
            const loader = availableModules[name];

            if (!loader) {
                Logger.info(`Module ${name} not found in the registry. Skipping.`);
                return;
            }

            try {
                const module = await loader();

                await module.default.init();

                Logger.success(`Module loaded successfully: ${name}`);
            } catch (error) {
                Logger.error(`Failed to load ${name} module:`, error);
            }
        });

        await Promise.all(loadPromises);
    }
}

export const modulesProvider = new ModulesProvider();